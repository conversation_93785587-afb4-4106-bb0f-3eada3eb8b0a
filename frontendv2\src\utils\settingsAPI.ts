// Settings API utilities and interfaces

import { API_BASE_URL } from './api';

// Settings interfaces
export interface SiteSettings {
  general: {
    siteName: string;
    siteDescription: string;
    contactEmail: string;
    contactPhone: string;
    address: string;
    timezone: string;
    currency: string;
    language: string;
  };
  features: {
    enableAppointments: boolean;
    enableEcommerce: boolean;
    enableReviews: boolean;
    enableLoyaltyProgram: boolean;
    enableGiftCards: boolean;
    enableWaitlist: boolean;
    enableReferrals: boolean;
  };
  notifications: {
    emailNotifications: boolean;
    smsNotifications: boolean;
    pushNotifications: boolean;
    appointmentReminders: boolean;
    marketingEmails: boolean;
  };
}

export interface PaymentSettings {
  methods: {
    cashApp: {
      enabled: boolean;
      handle: string;
    };
    zelle: {
      enabled: boolean;
      email: string;
      phone: string;
    };
    venmo: {
      enabled: boolean;
      handle: string;
    };
    paypal: {
      enabled: boolean;
      email: string;
    };
  };
  policies: {
    requireDeposit: boolean;
    depositAmount: number;
    depositPercentage: number;
    cancellationPolicy: string;
    refundPolicy: string;
  };
}

export interface ThemeSettings {
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
  fonts: {
    heading: string;
    body: string;
  };
  layout: {
    headerStyle: string;
    footerStyle: string;
    sidebarPosition: string;
  };
}

export interface SEOSettings {
  meta: {
    title: string;
    description: string;
    keywords: string;
    author: string;
  };
  social: {
    ogTitle: string;
    ogDescription: string;
    ogImage: string;
    twitterCard: string;
  };
  analytics: {
    googleAnalyticsId: string;
    facebookPixelId: string;
    googleTagManagerId: string;
  };
}

export interface AllSettings {
  site: SiteSettings;
  payment: PaymentSettings;
  theme: ThemeSettings;
  seo: SEOSettings;
}

// Helper function for API requests
async function apiRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
  const token = localStorage.getItem('authToken') || localStorage.getItem('token');
  
  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  };

  const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
  
  if (!response.ok) {
    const error = await response.json().catch(() => ({ message: 'Network error' }));
    throw new Error(error.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
}

// Settings API functions
export const settingsAPI = {
  // Get all settings
  getAllSettings: async (): Promise<{ success: boolean; data: AllSettings }> => {
    return await apiRequest('/admin/settings');
  },

  // Update site settings
  updateSiteSettings: async (settings: Partial<SiteSettings>): Promise<{ success: boolean; data: SiteSettings }> => {
    return await apiRequest('/admin/settings/site', {
      method: 'PUT',
      body: JSON.stringify(settings),
    });
  },

  // Update payment settings
  updatePaymentSettings: async (settings: Partial<PaymentSettings>): Promise<{ success: boolean; data: PaymentSettings }> => {
    return await apiRequest('/admin/settings/payment', {
      method: 'PUT',
      body: JSON.stringify(settings),
    });
  },

  // Update theme settings
  updateThemeSettings: async (settings: Partial<ThemeSettings>): Promise<{ success: boolean; data: ThemeSettings }> => {
    return await apiRequest('/admin/settings/theme', {
      method: 'PUT',
      body: JSON.stringify(settings),
    });
  },

  // Update SEO settings
  updateSEOSettings: async (settings: Partial<SEOSettings>): Promise<{ success: boolean; data: SEOSettings }> => {
    return await apiRequest('/admin/settings/seo', {
      method: 'PUT',
      body: JSON.stringify(settings),
    });
  },
};
