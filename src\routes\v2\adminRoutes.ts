import { Router } from 'express';
import { Request, Response } from 'express';
import { User, Appointment, PaymentConfirmation, Service } from '../../models';
import { authenticate, authorize } from '../../middleware/auth';
import { sendSuccess, sendError } from '../../utils/response';
import { AuthenticatedRequest } from '../../types';
import { EmailService } from '../../services/emailService';

const router = Router();

// All admin routes require authentication and admin role
router.use(authenticate);
router.use(authorize('admin'));

// GET /api/v2/admin/dashboard - Get admin dashboard overview
router.get('/dashboard', async (req: AuthenticatedRequest, res: Response) => {
  try {
    // Get counts for dashboard overview
    const totalUsers = await User.countDocuments({ role: 'user' });
    const totalAppointments = await Appointment.countDocuments();
    const totalServices = await Service.countDocuments({ isActive: true });
    const pendingPayments = await PaymentConfirmation.countDocuments({ status: 'pending' });
    
    // Get recent appointments
    const recentAppointments = await Appointment.find()
      .populate('user', 'firstName lastName email')
      .populate('service', 'name price')
      .sort({ createdAt: -1 })
      .limit(10);
    
    // Get pending payment confirmations
    const pendingPaymentConfirmations = await PaymentConfirmation.find({ status: 'pending' })
      .populate('user', 'firstName lastName email')
      .populate([
        { path: 'appointment', populate: { path: 'service', select: 'name' } },
        { path: 'order' }
      ])
      .sort({ createdAt: -1 })
      .limit(10);
    
    // Get appointment statistics by status
    const appointmentStats = await Appointment.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);
    
    // Get payment confirmation statistics
    const paymentStats = await PaymentConfirmation.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      }
    ]);
    
    // Format recent appointments with robust null checks
    const formattedAppointments = recentAppointments
      .filter(appointment => {
        // Check if appointment has valid user and service references
        return appointment &&
               appointment.user &&
               appointment.service &&
               (appointment.user as any)._id &&
               (appointment.service as any)._id;
      })
      .map(appointment => ({
        id: appointment._id,
        user: {
          id: (appointment.user as any)._id,
          name: `${(appointment.user as any).firstName || ''} ${(appointment.user as any).lastName || ''}`.trim(),
          email: (appointment.user as any).email || ''
        },
        service: {
          id: (appointment.service as any)._id,
          name: (appointment.service as any).name || '',
          price: (appointment.service as any).price || 0
        },
        date: appointment.date,
        time: appointment.time,
        status: appointment.status,
        totalAmount: (appointment.service as any).price || 0,
        createdAt: appointment.createdAt
      }));

    // Format pending payment confirmations with robust null checks
    const formattedPaymentConfirmations = pendingPaymentConfirmations
      .filter(confirmation => {
        // Check if confirmation has valid user reference
        return confirmation &&
               confirmation.user &&
               (confirmation.user as any)._id;
      })
      .map(confirmation => ({
        id: confirmation._id,
        user: {
          id: (confirmation.user as any)._id,
          name: `${(confirmation.user as any).firstName || ''} ${(confirmation.user as any).lastName || ''}`.trim(),
          email: (confirmation.user as any).email || ''
        },
        appointmentId: confirmation.appointment?._id,
        appointmentService: (confirmation.appointment as any)?.service?.name || '',
        orderId: confirmation.order?._id,
        amount: confirmation.amount || 0,
        paymentMethod: confirmation.paymentMethod || '',
        proofImage: confirmation.proofImage || '',
        createdAt: confirmation.createdAt
      }));
    
    const dashboardData = {
      overview: {
        totalUsers,
        totalAppointments,
        totalServices,
        pendingPayments
      },
      statistics: {
        appointments: appointmentStats.reduce((acc, stat) => {
          acc[stat._id] = stat.count;
          return acc;
        }, {} as any),
        payments: paymentStats.reduce((acc, stat) => {
          acc[stat._id] = {
            count: stat.count,
            totalAmount: stat.totalAmount
          };
          return acc;
        }, {} as any)
      },
      recentAppointments: formattedAppointments,
      pendingPaymentConfirmations: formattedPaymentConfirmations
    };
    
    sendSuccess(res, 'Admin dashboard data retrieved successfully', dashboardData);
  } catch (error) {
    console.error('Get admin dashboard error:', error);
    sendError(res, (error as Error).message);
  }
});


// GET /api/v2/admin/appointments - Get all appointments with filtering and pagination
router.get('/appointments', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      date,
      search,
      sortBy = 'date',
      sortOrder = 'desc'
    } = req.query as any;

    const filter: any = {};

    // Status filter
    if (status && status !== 'all') {
      filter.status = status;
    }

    // Date filter
    if (date) {
      const searchDate = new Date(date);
      const nextDay = new Date(searchDate);
      nextDay.setDate(nextDay.getDate() + 1);
      filter.date = {
        $gte: searchDate,
        $lt: nextDay
      };
    }

    // Search filter
    if (search) {
      filter.$or = [
        { 'customerInfo.name': { $regex: search, $options: 'i' } },
        { 'customerInfo.email': { $regex: search, $options: 'i' } },
        { 'customerInfo.phone': { $regex: search, $options: 'i' } }
      ];
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions: any = {};
    sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const appointments = await Appointment.find(filter)
      .populate('user', 'firstName lastName email phone')
      .populate('service', 'name price duration category')
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Appointment.countDocuments(filter);

    const responseData = {
      appointments: appointments.map(apt => {
        return {
          id: apt._id,
          user: apt.user ? {
            id: (apt.user as any)._id,
            name: `${(apt.user as any).firstName || ''} ${(apt.user as any).lastName || ''}`.trim(),
            email: (apt.user as any).email,
            phone: (apt.user as any).phone
          } : null,
          customerInfo: apt.customerInfo,
          service: apt.service ? {
            id: (apt.service as any)._id,
            name: (apt.service as any).name,
            price: (apt.service as any).price,
            duration: (apt.service as any).duration,
            category: (apt.service as any).category
          } : null,
          date: apt.date,
          time: apt.time,
          status: apt.status,
          paymentStatus: apt.paymentStatus,
          message: apt.message,
          paymentProofs: (apt as any).paymentProofs || [],
          createdAt: apt.createdAt,
          updatedAt: apt.updatedAt
        };
      }),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    };

    sendSuccess(res, 'Appointments retrieved successfully', responseData);
  } catch (error) {
    console.error('Get appointments error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/admin/appointments/:id - Get single appointment details
router.get('/appointments/:id', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;

    const appointment = await Appointment.findById(id)
      .populate('user', 'firstName lastName email phone')
      .populate('service', 'name price duration category description');

    if (!appointment) {
      sendError(res, 'Appointment not found', undefined, 404);
      return;
    }

    const responseData = {
      id: appointment._id,
      user: appointment.user ? {
        id: (appointment.user as any)._id,
        name: `${(appointment.user as any).firstName || ''} ${(appointment.user as any).lastName || ''}`.trim(),
        email: (appointment.user as any).email,
        phone: (appointment.user as any).phone
      } : null,
      customerInfo: appointment.customerInfo,
      service: appointment.service ? {
        id: (appointment.service as any)._id,
        name: (appointment.service as any).name,
        price: (appointment.service as any).price,
        duration: (appointment.service as any).duration,
        category: (appointment.service as any).category,
        description: (appointment.service as any).description
      } : null,
      date: appointment.date,
      time: appointment.time,
      status: appointment.status,
      message: appointment.message,
      createdAt: appointment.createdAt,
      updatedAt: appointment.updatedAt
    };

    sendSuccess(res, 'Appointment details retrieved successfully', responseData);
  } catch (error) {
    console.error('Get appointment details error:', error);
    sendError(res, (error as Error).message);
  }
});

// PUT /api/v2/admin/appointments/:id - Update appointment
router.put('/appointments/:id', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { date, time, customerInfo, message, status } = req.body;

    const appointment = await Appointment.findById(id);

    if (!appointment) {
      sendError(res, 'Appointment not found', undefined, 404);
      return;
    }

    // Update fields if provided
    if (date) appointment.date = new Date(date);
    if (time) appointment.time = time;
    if (customerInfo) appointment.customerInfo = { ...appointment.customerInfo, ...customerInfo };
    if (message !== undefined) appointment.message = message;
    if (status) appointment.status = status;

    await appointment.save();

    // Populate for response
    await appointment.populate('user', 'firstName lastName email phone');
    await appointment.populate('service', 'name price duration category');

    const responseData = {
      id: appointment._id,
      user: appointment.user ? {
        id: (appointment.user as any)._id,
        name: `${(appointment.user as any).firstName || ''} ${(appointment.user as any).lastName || ''}`.trim(),
        email: (appointment.user as any).email,
        phone: (appointment.user as any).phone
      } : null,
      customerInfo: appointment.customerInfo,
      service: appointment.service ? {
        id: (appointment.service as any)._id,
        name: (appointment.service as any).name,
        price: (appointment.service as any).price,
        duration: (appointment.service as any).duration,
        category: (appointment.service as any).category
      } : null,
      date: appointment.date,
      time: appointment.time,
      status: appointment.status,
      message: appointment.message,
      updatedAt: appointment.updatedAt
    };

    sendSuccess(res, 'Appointment updated successfully', responseData);
  } catch (error) {
    console.error('Update appointment error:', error);
    sendError(res, (error as Error).message);
  }
});

// PUT /api/v2/admin/appointments/:id/status - Update appointment status
router.put('/appointments/:id/status', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { status, sendNotification = true } = req.body;

    if (!['pending', 'confirmed', 'completed', 'cancelled'].includes(status)) {
      sendError(res, 'Invalid status', undefined, 400);
      return;
    }

    const appointment = await Appointment.findById(id)
      .populate('user', 'firstName lastName email')
      .populate('service', 'name price');

    if (!appointment) {
      sendError(res, 'Appointment not found', undefined, 404);
      return;
    }

    const previousStatus = appointment.status;
    appointment.status = status;
    await appointment.save();

    // Send email notification if status changed and notification is enabled
    if (sendNotification && previousStatus !== status) {
      try {
        const emailService = new EmailService();
        const userInfo = {
          name: appointment.user ?
            `${(appointment.user as any).firstName || ''} ${(appointment.user as any).lastName || ''}`.trim() :
            appointment.customerInfo.name,
          email: appointment.user ?
            (appointment.user as any).email :
            appointment.customerInfo.email,
          firstName: appointment.user ?
            (appointment.user as any).firstName :
            appointment.customerInfo.name.split(' ')[0],
          lastName: appointment.user ?
            (appointment.user as any).lastName :
            appointment.customerInfo.name.split(' ').slice(1).join(' ')
        };

        const appointmentInfo = {
          _id: appointment._id,
          service: (appointment.service as any).name,
          date: appointment.date,
          time: appointment.time,
          status: appointment.status
        };

        // Send appropriate email based on status
        switch (status) {
          case 'confirmed':
            await emailService.sendAppointmentConfirmation(userInfo, appointmentInfo);
            break;
          case 'cancelled':
            await emailService.sendAppointmentCancellation(userInfo, appointmentInfo);
            break;
          case 'completed':
            // Could send a thank you email or request for review
            break;
        }
      } catch (emailError) {
        console.error('Failed to send email notification:', emailError);
        // Don't fail the request if email fails
      }
    }

    const responseData = {
      id: appointment._id,
      status: appointment.status,
      user: appointment.user ? {
        name: `${(appointment.user as any).firstName || ''} ${(appointment.user as any).lastName || ''}`.trim(),
        email: (appointment.user as any).email
      } : appointment.customerInfo,
      service: {
        name: (appointment.service as any).name
      },
      date: appointment.date,
      time: appointment.time,
      updatedAt: appointment.updatedAt,
      emailSent: sendNotification && previousStatus !== status
    };

    sendSuccess(res, 'Appointment status updated successfully', responseData);
  } catch (error) {
    console.error('Update appointment status error:', error);
    sendError(res, (error as Error).message);
  }
});

// DELETE /api/v2/admin/appointments/:id - Delete appointment
router.delete('/appointments/:id', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;

    const appointment = await Appointment.findById(id);

    if (!appointment) {
      sendError(res, 'Appointment not found', undefined, 404);
      return;
    }

    await Appointment.findByIdAndDelete(id);

    sendSuccess(res, 'Appointment deleted successfully', { id });
  } catch (error) {
    console.error('Delete appointment error:', error);
    sendError(res, (error as Error).message);
  }
});

// POST /api/v2/admin/appointments - Create new appointment
router.post('/appointments', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { userId, service, date, time, customerInfo, message, sendConfirmation = true } = req.body;

    // Check if time slot is available
    const existingAppointment = await Appointment.findOne({
      date: new Date(date),
      time,
      status: { $in: ['pending', 'confirmed'] }
    });

    if (existingAppointment) {
      sendError(res, 'Time slot is already booked', undefined, 400);
      return;
    }

    const appointmentData: any = {
      service,
      date: new Date(date),
      time,
      customerInfo,
      message,
      status: 'pending'
    };

    if (userId && userId !== 'temp-user') {
      appointmentData.user = userId;
    }

    const appointment = new Appointment(appointmentData);
    await appointment.save();

    // Populate for response
    await appointment.populate('user', 'firstName lastName email phone');
    await appointment.populate('service', 'name price duration category');

    // Send confirmation email if requested
    if (sendConfirmation) {
      try {
        const emailService = new EmailService();
        const userInfo = {
          name: appointment.user ?
            `${(appointment.user as any).firstName || ''} ${(appointment.user as any).lastName || ''}`.trim() :
            appointment.customerInfo.name,
          email: appointment.user ?
            (appointment.user as any).email :
            appointment.customerInfo.email,
          firstName: appointment.user ?
            (appointment.user as any).firstName :
            appointment.customerInfo.name.split(' ')[0],
          lastName: appointment.user ?
            (appointment.user as any).lastName :
            appointment.customerInfo.name.split(' ').slice(1).join(' ')
        };

        const appointmentInfo = {
          _id: appointment._id,
          service: appointment.service ? (appointment.service as any).name : 'Unknown Service',
          date: appointment.date,
          time: appointment.time,
          status: appointment.status
        };

        await emailService.sendAppointmentConfirmation(userInfo, appointmentInfo);
      } catch (emailError) {
        console.error('Failed to send confirmation email:', emailError);
        // Don't fail the request if email fails
      }
    }

    const responseData = {
      id: appointment._id,
      user: appointment.user ? {
        id: (appointment.user as any)._id,
        name: `${(appointment.user as any).firstName || ''} ${(appointment.user as any).lastName || ''}`.trim(),
        email: (appointment.user as any).email,
        phone: (appointment.user as any).phone
      } : null,
      customerInfo: appointment.customerInfo,
      service: appointment.service ? {
        id: (appointment.service as any)._id,
        name: (appointment.service as any).name,
        price: (appointment.service as any).price,
        duration: (appointment.service as any).duration,
        category: (appointment.service as any).category
      } : null,
      date: appointment.date,
      time: appointment.time,
      status: appointment.status,
      message: appointment.message,
      createdAt: appointment.createdAt,
      emailSent: sendConfirmation
    };

    sendSuccess(res, 'Appointment created successfully', responseData);
  } catch (error) {
    console.error('Create appointment error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/admin/appointments/analytics - Get appointment analytics
router.get('/appointments/analytics', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { startDate, endDate } = req.query as any;

    const dateFilter: any = {};
    if (startDate && endDate) {
      dateFilter.date = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    // Get appointment statistics by status
    const statusStats = await Appointment.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get appointments by date for chart
    const dailyStats = await Appointment.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$date' }
          },
          count: { $sum: 1 },
          confirmed: {
            $sum: { $cond: [{ $eq: ['$status', 'confirmed'] }, 1, 0] }
          },
          completed: {
            $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
          },
          cancelled: {
            $sum: { $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0] }
          }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    // Get service popularity
    const serviceStats = await Appointment.aggregate([
      { $match: dateFilter },
      {
        $lookup: {
          from: 'services',
          localField: 'service',
          foreignField: '_id',
          as: 'serviceInfo'
        }
      },
      { $unwind: '$serviceInfo' },
      {
        $group: {
          _id: '$service',
          serviceName: { $first: '$serviceInfo.name' },
          count: { $sum: 1 },
          revenue: { $sum: '$serviceInfo.price' }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    // Calculate revenue metrics
    const revenueStats = await Appointment.aggregate([
      { $match: { ...dateFilter, status: 'completed' } },
      {
        $lookup: {
          from: 'services',
          localField: 'service',
          foreignField: '_id',
          as: 'serviceInfo'
        }
      },
      { $unwind: '$serviceInfo' },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$serviceInfo.price' },
          averageRevenue: { $avg: '$serviceInfo.price' },
          totalAppointments: { $sum: 1 }
        }
      }
    ]);

    // Get today's appointments
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayAppointments = await Appointment.countDocuments({
      date: { $gte: today, $lt: tomorrow }
    });

    const responseData = {
      statusStats: statusStats.reduce((acc, stat) => {
        acc[stat._id] = stat.count;
        return acc;
      }, {}),
      dailyStats,
      serviceStats,
      revenueStats: revenueStats[0] || {
        totalRevenue: 0,
        averageRevenue: 0,
        totalAppointments: 0
      },
      todayAppointments,
      totalAppointments: await Appointment.countDocuments(dateFilter)
    };

    sendSuccess(res, 'Appointment analytics retrieved successfully', responseData);
  } catch (error) {
    console.error('Get appointment analytics error:', error);
    sendError(res, (error as Error).message);
  }
});

// PUT /api/v2/admin/appointments/bulk-status - Bulk update appointment status
router.put('/appointments/bulk-status', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { appointmentIds, status } = req.body;

    if (!Array.isArray(appointmentIds) || appointmentIds.length === 0) {
      sendError(res, 'Appointment IDs array is required', undefined, 400);
      return;
    }

    if (!['pending', 'confirmed', 'completed', 'cancelled'].includes(status)) {
      sendError(res, 'Invalid status', undefined, 400);
      return;
    }

    const result = await Appointment.updateMany(
      { _id: { $in: appointmentIds } },
      { status, updatedAt: new Date() }
    );

    sendSuccess(res, 'Appointments updated successfully', {
      updatedCount: result.modifiedCount,
      status
    });
  } catch (error) {
    console.error('Bulk update appointments error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/admin/payment-confirmations - Get all payment confirmations with pagination and filters
router.get('/payment-confirmations', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const status = req.query.status as string;

    const query: any = {};
    if (status) {
      query.status = status;
    }

    const paymentConfirmations = await PaymentConfirmation.find(query)
      .populate('user', 'firstName lastName email phone')
      .populate([
        { path: 'appointment', populate: { path: 'service', select: 'name price' } },
        { path: 'order' }
      ])
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit);

    const total = await PaymentConfirmation.countDocuments(query);

    const formattedConfirmations = paymentConfirmations.map(confirmation => ({
      id: confirmation._id,
      user: {
        id: (confirmation.user as any)._id,
        name: `${(confirmation.user as any).firstName} ${(confirmation.user as any).lastName}`,
        email: (confirmation.user as any).email,
        phone: (confirmation.user as any).phone
      },
      appointmentId: confirmation.appointment?._id,
      appointmentService: (confirmation.appointment as any)?.service?.name,
      appointmentServicePrice: (confirmation.appointment as any)?.service?.price,
      orderId: confirmation.order?._id,
      amount: confirmation.amount,
      paymentMethod: confirmation.paymentMethod,
      proofImage: confirmation.proofImage,
      notes: confirmation.notes,
      status: confirmation.status,
      verifiedAt: confirmation.verifiedAt,
      rejectionReason: confirmation.rejectionReason,
      createdAt: confirmation.createdAt
    }));

    sendSuccess(res, 'Payment confirmations retrieved successfully', {
      paymentConfirmations: formattedConfirmations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get admin payment confirmations error:', error);
    sendError(res, (error as Error).message);
  }
});

// PUT /api/v2/admin/payment-confirmations/:id/status - Update payment confirmation status
router.put('/payment-confirmations/:id/status', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { status, rejectionReason } = req.body;

    if (!['verified', 'rejected'].includes(status)) {
      sendError(res, 'Invalid status. Must be "verified" or "rejected"', undefined, 400);
      return;
    }

    if (status === 'rejected' && !rejectionReason) {
      sendError(res, 'Rejection reason is required when rejecting payment confirmation', undefined, 400);
      return;
    }

    const paymentConfirmation = await PaymentConfirmation.findById(id)
      .populate('user', 'firstName lastName email')
      .populate([
        { path: 'appointment', populate: { path: 'service', select: 'name' } },
        { path: 'order' }
      ]);

    if (!paymentConfirmation) {
      sendError(res, 'Payment confirmation not found', undefined, 404);
      return;
    }

    paymentConfirmation.status = status;
    paymentConfirmation.verifiedBy = req.user!._id as any;
    paymentConfirmation.verifiedAt = new Date();

    if (status === 'rejected') {
      paymentConfirmation.rejectionReason = rejectionReason;
    }

    await paymentConfirmation.save();

    const responseData = {
      id: paymentConfirmation._id,
      status: paymentConfirmation.status,
      user: {
        name: `${(paymentConfirmation.user as any).firstName} ${(paymentConfirmation.user as any).lastName}`,
        email: (paymentConfirmation.user as any).email
      },
      amount: paymentConfirmation.amount,
      paymentMethod: paymentConfirmation.paymentMethod,
      verifiedAt: paymentConfirmation.verifiedAt,
      rejectionReason: paymentConfirmation.rejectionReason,
      updatedAt: paymentConfirmation.updatedAt
    };

    sendSuccess(res, 'Payment confirmation status updated successfully', responseData);
  } catch (error) {
    console.error('Update payment confirmation status error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/admin/customers - Get all customers with filtering and pagination
router.get('/customers', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query as any;

    const filter: any = { role: 'user' };

    // Search filter
    if (search) {
      filter.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } }
      ];
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions: any = {};
    sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const customers = await User.find(filter)
      .select('-password')
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit));

    const total = await User.countDocuments(filter);

    // Get appointment statistics for each customer
    const customerIds = customers.map(customer => customer._id);
    const appointmentStats = await Appointment.aggregate([
      { $match: { user: { $in: customerIds } } },
      {
        $group: {
          _id: '$user',
          totalAppointments: { $sum: 1 },
          totalSpent: { $sum: '$totalPrice' },
          lastAppointment: { $max: '$date' }
        }
      }
    ]);

    // Create a map for quick lookup
    const statsMap = new Map();
    appointmentStats.forEach(stat => {
      statsMap.set(stat._id.toString(), stat);
    });

    const responseData = {
      customers: customers.map(customer => {
        const stats = statsMap.get(customer._id.toString()) || {
          totalAppointments: 0,
          totalSpent: 0,
          lastAppointment: null
        };

        return {
          id: customer._id,
          firstName: customer.firstName,
          lastName: customer.lastName,
          email: customer.email,
          phone: customer.phone,
          totalAppointments: stats.totalAppointments,
          totalSpent: stats.totalSpent,
          lastAppointment: stats.lastAppointment,
          status: customer.isVerified ? 'active' : 'inactive',
          createdAt: customer.createdAt,
          updatedAt: customer.updatedAt
        };
      }),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    };

    sendSuccess(res, 'Customers retrieved successfully', responseData);
  } catch (error) {
    console.error('Get customers error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/admin/customers/:id - Get single customer details
router.get('/customers/:id', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;

    const customer = await User.findById(id).select('-password');

    if (!customer) {
      sendError(res, 'Customer not found', undefined, 404);
      return;
    }

    // Get customer's appointments
    const appointments = await Appointment.find({ user: id })
      .populate('service', 'name price duration category')
      .sort({ date: -1 })
      .limit(10);

    // Get customer statistics
    const stats = await Appointment.aggregate([
      { $match: { user: customer._id } },
      {
        $group: {
          _id: null,
          totalAppointments: { $sum: 1 },
          totalSpent: { $sum: '$totalPrice' },
          lastAppointment: { $max: '$date' }
        }
      }
    ]);

    const customerStats = stats[0] || {
      totalAppointments: 0,
      totalSpent: 0,
      lastAppointment: null
    };

    const responseData = {
      id: customer._id,
      firstName: customer.firstName,
      lastName: customer.lastName,
      email: customer.email,
      phone: customer.phone,
      isVerified: customer.isVerified,
      notificationPreferences: customer.notificationPreferences,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
      statistics: customerStats,
      recentAppointments: appointments.map(apt => ({
        id: apt._id,
        service: apt.service ? {
          id: (apt.service as any)._id,
          name: (apt.service as any).name,
          price: (apt.service as any).price
        } : null,
        date: apt.date,
        time: apt.time,
        status: apt.status,
        totalPrice: apt.totalPrice
      }))
    };

    sendSuccess(res, 'Customer details retrieved successfully', responseData);
  } catch (error) {
    console.error('Get customer details error:', error);
    sendError(res, (error as Error).message);
  }
});

// PUT /api/v2/admin/customers/:id - Update customer
router.put('/customers/:id', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { firstName, lastName, phone, notificationPreferences } = req.body;

    const customer = await User.findById(id);

    if (!customer) {
      sendError(res, 'Customer not found', undefined, 404);
      return;
    }

    // Update fields if provided
    if (firstName) customer.firstName = firstName;
    if (lastName) customer.lastName = lastName;
    if (phone) customer.phone = phone;
    if (notificationPreferences) {
      customer.notificationPreferences = {
        ...customer.notificationPreferences,
        ...notificationPreferences
      };
    }

    await customer.save();

    const responseData = {
      id: customer._id,
      firstName: customer.firstName,
      lastName: customer.lastName,
      email: customer.email,
      phone: customer.phone,
      isVerified: customer.isVerified,
      notificationPreferences: customer.notificationPreferences,
      updatedAt: customer.updatedAt
    };

    sendSuccess(res, 'Customer updated successfully', responseData);
  } catch (error) {
    console.error('Update customer error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/admin/services - Get all services with filtering and pagination
router.get('/services', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      category,
      isActive,
      sortBy = 'name',
      sortOrder = 'asc'
    } = req.query as any;

    const filter: any = {};

    // Category filter
    if (category && category !== 'all') {
      filter.category = category;
    }

    // Active filter
    if (isActive !== undefined) {
      filter.isActive = isActive === 'true';
    }

    // Search filter
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { category: { $regex: search, $options: 'i' } }
      ];
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions: any = {};
    sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const services = await Service.find(filter)
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Service.countDocuments(filter);

    const responseData = {
      services: services.map(service => ({
        id: service._id,
        name: service.name,
        description: service.description,
        category: service.category,
        duration: service.duration,
        price: service.price,
        isActive: service.isActive,
        image: service.image,
        images: service.images,
        createdAt: service.createdAt,
        updatedAt: service.updatedAt
      })),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    };

    sendSuccess(res, 'Services retrieved successfully', responseData);
  } catch (error) {
    console.error('Get services error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/admin/services/:id - Get single service details
router.get('/services/:id', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;

    const service = await Service.findById(id);

    if (!service) {
      sendError(res, 'Service not found', undefined, 404);
      return;
    }

    const responseData = {
      id: service._id,
      name: service.name,
      description: service.description,
      category: service.category,
      duration: service.duration,
      price: service.price,
      isActive: service.isActive,
      image: service.image,
      images: service.images,
      createdAt: service.createdAt,
      updatedAt: service.updatedAt
    };

    sendSuccess(res, 'Service details retrieved successfully', responseData);
  } catch (error) {
    console.error('Get service details error:', error);
    sendError(res, (error as Error).message);
  }
});

// POST /api/v2/admin/services - Create new service
router.post('/services', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { name, description, category, duration, price, isActive = true, image, images } = req.body;

    // Validate required fields
    if (!name || !description || !category || !duration || price === undefined) {
      sendError(res, 'Name, description, category, duration, and price are required', undefined, 400);
      return;
    }

    const service = new Service({
      name,
      description,
      category,
      duration,
      price,
      isActive,
      image,
      images: images || []
    });

    await service.save();

    const responseData = {
      id: service._id,
      name: service.name,
      description: service.description,
      category: service.category,
      duration: service.duration,
      price: service.price,
      isActive: service.isActive,
      image: service.image,
      images: service.images,
      createdAt: service.createdAt,
      updatedAt: service.updatedAt
    };

    sendSuccess(res, 'Service created successfully', responseData);
  } catch (error) {
    console.error('Create service error:', error);
    sendError(res, (error as Error).message);
  }
});

// PUT /api/v2/admin/services/:id - Update service
router.put('/services/:id', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { name, description, category, duration, price, isActive, image, images } = req.body;

    const service = await Service.findById(id);

    if (!service) {
      sendError(res, 'Service not found', undefined, 404);
      return;
    }

    // Update fields if provided
    if (name) service.name = name;
    if (description) service.description = description;
    if (category) service.category = category;
    if (duration !== undefined) service.duration = duration;
    if (price !== undefined) service.price = price;
    if (isActive !== undefined) service.isActive = isActive;
    if (image !== undefined) service.image = image;
    if (images !== undefined) service.images = images;

    await service.save();

    const responseData = {
      id: service._id,
      name: service.name,
      description: service.description,
      category: service.category,
      duration: service.duration,
      price: service.price,
      isActive: service.isActive,
      image: service.image,
      images: service.images,
      updatedAt: service.updatedAt
    };

    sendSuccess(res, 'Service updated successfully', responseData);
  } catch (error) {
    console.error('Update service error:', error);
    sendError(res, (error as Error).message);
  }
});

// DELETE /api/v2/admin/services/:id - Delete service
router.delete('/services/:id', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;

    const service = await Service.findById(id);

    if (!service) {
      sendError(res, 'Service not found', undefined, 404);
      return;
    }

    // Check if service is used in any appointments
    const appointmentCount = await Appointment.countDocuments({ service: id });

    if (appointmentCount > 0) {
      // Instead of deleting, mark as inactive
      service.isActive = false;
      await service.save();

      sendSuccess(res, 'Service deactivated successfully (has existing appointments)', {
        id: service._id,
        isActive: service.isActive
      });
    } else {
      // Safe to delete
      await Service.findByIdAndDelete(id);
      sendSuccess(res, 'Service deleted successfully', { id });
    }
  } catch (error) {
    console.error('Delete service error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/admin/settings - Get all admin settings
router.get('/settings', async (_req: AuthenticatedRequest, res: Response) => {
  try {
    // Import models dynamically to avoid circular dependencies
    const { SiteSettings, PaymentSettings, ThemeSettings, SEO } = require('../../models');

    // Get all settings in parallel
    const [siteSettings, paymentSettings, themeSettings, seoSettings] = await Promise.all([
      SiteSettings.findOne() || SiteSettings.create({}),
      PaymentSettings.findOne() || PaymentSettings.create({}),
      ThemeSettings.findOne({ isActive: true }) || ThemeSettings.create({}),
      SEO.findOne() || SEO.create({})
    ]);

    const responseData = {
      site: siteSettings,
      payment: paymentSettings,
      theme: themeSettings,
      seo: seoSettings
    };

    sendSuccess(res, 'Settings retrieved successfully', responseData);
  } catch (error) {
    console.error('Get settings error:', error);
    sendError(res, (error as Error).message);
  }
});

// PUT /api/v2/admin/settings/site - Update site settings
router.put('/settings/site', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { SiteSettings } = require('../../models');
    const updateData = req.body;

    let settings = await SiteSettings.findOne();

    if (!settings) {
      settings = await SiteSettings.create(updateData);
    } else {
      // Deep merge the update data
      Object.keys(updateData).forEach(key => {
        if (typeof updateData[key] === 'object' && updateData[key] !== null) {
          (settings as any)[key] = { ...(settings as any)[key], ...updateData[key] };
        } else {
          (settings as any)[key] = updateData[key];
        }
      });
      await settings.save();
    }

    sendSuccess(res, 'Site settings updated successfully', settings);
  } catch (error) {
    console.error('Update site settings error:', error);
    sendError(res, (error as Error).message);
  }
});

// PUT /api/v2/admin/settings/payment - Update payment settings
router.put('/settings/payment', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { PaymentSettings } = require('../../models');
    const updateData = req.body;

    let settings = await PaymentSettings.findOne();

    if (!settings) {
      settings = await PaymentSettings.create(updateData);
    } else {
      Object.assign(settings, updateData);
      await settings.save();
    }

    sendSuccess(res, 'Payment settings updated successfully', settings);
  } catch (error) {
    console.error('Update payment settings error:', error);
    sendError(res, (error as Error).message);
  }
});

// PUT /api/v2/admin/settings/theme - Update theme settings
router.put('/settings/theme', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { ThemeSettings } = require('../../models');
    const updateData = req.body;

    let settings = await ThemeSettings.findOne({ isActive: true });

    if (!settings) {
      settings = await ThemeSettings.create({ ...updateData, isActive: true });
    } else {
      Object.assign(settings, updateData);
      await settings.save();
    }

    sendSuccess(res, 'Theme settings updated successfully', settings);
  } catch (error) {
    console.error('Update theme settings error:', error);
    sendError(res, (error as Error).message);
  }
});

// PUT /api/v2/admin/settings/seo - Update SEO settings
router.put('/settings/seo', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { SEO } = require('../../models');
    const updateData = req.body;

    let settings = await SEO.findOne();

    if (!settings) {
      settings = await SEO.create(updateData);
    } else {
      Object.assign(settings, updateData);
      await settings.save();
    }

    sendSuccess(res, 'SEO settings updated successfully', settings);
  } catch (error) {
    console.error('Update SEO settings error:', error);
    sendError(res, (error as Error).message);
  }
});

export default router;
